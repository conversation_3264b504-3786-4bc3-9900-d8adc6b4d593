import 'dart:convert';
import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:map_launcher/map_launcher.dart';
import 'package:page/src/core/utils/main_cached_image.dart';
import 'package:page/src/features/controllers/auth_controller.dart';
import 'package:page/src/features/models/agents.dart';
import 'package:page/src/features/models/category.dart';
import 'package:page/src/features/views/property_details/widgets/property_details.dart';
import 'package:page/src/features/views/send_holiday_request/send_holiday_home_request.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../../core/localization/app_localizations.dart';
import '../../../../core/shared_widgets/shared_widgets.dart';
import '../../account/account.dart';
import '../holiday_home_details.dart';

Future<void> detailsHolidayHome(
  BuildContext context, {
  required title,
  required description,
  required roomnumber,
  required agentId,
  required startprice,
  required video,
  required currencyController,
  required location,
  required type,
  required lat,
  required lng,
  required size,
  required int id,
  required int? rmsCategoryId,
  bool fromMaps = false,
  bool fromReels = false,
  String? image,
  AgentModel? agent,
}) {
  return showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Padding(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          child: Container(
            height: MediaQuery.of(context).size.height * 0.70,
            decoration: BoxDecoration(
                color: const Color(0xffF5F6F7),
                borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(25.0),
                    topRight: Radius.circular(25.0)),
                border: Border.all(color: Colors.black, width: 1.0)),
            child: SingleChildScrollView(
                child: Column(
              children: [
                const SizedBox(
                  height: 10,
                ),
                Container(height: 5, width: 50, color: const Color(0xffD2D4D6)),
                const SizedBox(
                  height: 20,
                ),
                Center(
                    child: Text(
                  title,
                  // AppLocalizations.of(context).translate('More Details'),
                  style: const TextStyle(fontWeight: FontWeight.bold),
                )),
                Container(
                  padding: const EdgeInsets.all(15),
                  child: Container(
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(10)),
                    child: Container(
                      padding: const EdgeInsets.all(15),
                      child: Column(
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (fromMaps) ...[
                                SizedBox(
                                  height: 140,
                                  width: 100,
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(5),
                                    child: image != null && image.isNotEmpty
                                        ? MainCachedImage(
                                            image,
                                            fit: BoxFit.cover,
                                          )
                                        : Image.asset(
                                            'assets/Mask Group 2.png',
                                            height: 200,
                                            width: 100,
                                            fit: BoxFit.cover,
                                          ),
                                  ),
                                ),
                                const SizedBox(
                                  width: 20,
                                ),
                              ],
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      children: [
                                        Container(
                                            height: 30,
                                            // width: 120,
                                            color: const Color(0xffF1F1F1),
                                            child: Container(
                                                padding:
                                                    const EdgeInsets.all(5),
                                                child: FittedBox(
                                                  fit: BoxFit.scaleDown,
                                                  child: Text(
                                                    AppLocalizations.of(context)
                                                        .translate(
                                                            'holidayhome'),
                                                    style: const TextStyle(
                                                        fontWeight:
                                                            FontWeight.bold),
                                                    maxLines: 1,
                                                  ),
                                                ))),
                                        const SizedBox(
                                          width: 20,
                                        ),
                                        type != null
                                            ? Container(
                                                height: 30,
                                                color: const Color(0xffF1F1F1),
                                                child: Container(
                                                    padding:
                                                        const EdgeInsets.all(5),
                                                    child: Text(
                                                      type.toString(),
                                                      style: const TextStyle(
                                                          fontWeight:
                                                              FontWeight.bold),
                                                    )))
                                            : Container(),
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    Row(
                                      children: [
                                        Text(
                                            '${AppLocalizations.of(context).translate('location')}:'),
                                        const SizedBox(
                                          width: 4,
                                        ),
                                        location != null
                                            ? Text(
                                                location!,
                                                style: const TextStyle(
                                                    fontWeight:
                                                        FontWeight.bold),
                                              )
                                            : Container()
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    Row(
                                      children: [
                                        Text(
                                            '${AppLocalizations.of(context).translate('price')}: '),
                                        const SizedBox(
                                          width: 4,
                                        ),
                                        FittedBox(
                                          fit: BoxFit.scaleDown,
                                          child: Row(
                                            children: [
                                              startprice != null
                                                  ? Text(
                                                      parsedPrice(startprice),
                                                      style: const TextStyle(
                                                          fontWeight:
                                                              FontWeight.bold))
                                                  : Container(),
                                              Text(
                                                  ' ${currencyController.currency}',
                                                  style: const TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold))
                                            ],
                                          ),
                                        ),
                                        // Text(
                                        //   AppLocalizations.of(context)
                                        //       .translate('per night'),
                                        //   style: const TextStyle(
                                        //       fontWeight: FontWeight.bold),
                                        // ),
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    Row(
                                      children: [
                                        Text(
                                            '${AppLocalizations.of(context).translate('size')}:'),
                                        const SizedBox(
                                          width: 4,
                                        ),
                                        if (size != null)
                                          Text(
                                            '${parsedPrice(size)} ${AppLocalizations.of(context).translate('sqrf')}',
                                            style: const TextStyle(
                                                fontWeight: FontWeight.bold),
                                          )
                                        else
                                          Container()
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                            '${AppLocalizations.of(context).translate('Number of Bedrooms')}:'),
                                        const SizedBox(
                                          width: 4,
                                        ),
                                        roomnumber != null
                                            ? Flexible(
                                                child: Text(
                                                '${(jsonDecode(roomnumber.toString()) as List).join(' ${AppLocalizations.of(context).translate('Bedrooms')} / ')} ${AppLocalizations.of(context).translate('Bedrooms')}',
                                                style: const TextStyle(
                                                    fontWeight:
                                                        FontWeight.bold),
                                              ))
                                            : Container(),
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    const Divider(
                                      color: Colors.black12,
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    description != null
                                        ? Text(
                                            description!,
                                            softWrap: true,
                                          )
                                        : Container(),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    Divider(
                                      color: Colors.grey[100],
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                          Column(
                            children: [
                              //! View Location on Map
                              _ViewLocationOnMap(
                                lat: lat,
                                lng: lng,
                              ),
                              const SizedBox(
                                height: 20,
                              ),
                              _SendRequest(
                                id: id,
                                rmsCategoryId: rmsCategoryId,
                                numOfRooms: roomnumber,
                                agentId: agentId,
                                startPrice: startprice,
                                  propertyName: title,
                              ),
                              const SizedBox(
                                height: 20,
                              ),
                              if (fromMaps || fromReels)
                                _OpenHolidayHomeVideoDetails(
                                  video: VideoDetailsModel(
                                      id: id,
                                      agent: AgentModel(
                                        id: agentId,
                                      ),
                                      name: title,
                                      startprice: startprice,
                                      video: video,
                                      type: type,
                                      isFav: 0,
                                      description: description,
                                      locationName: location,
                                      latitude: lat,
                                      longitude: lng,
                                      size: size,
                                      roomnumber: roomnumber),
                                ),
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                ),

                const SizedBox(
                  height: 20,
                ),

                // agent details
                if (agent != null)
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 26),
                    child: Row(
                      children: [
                        // image then name then under it two icons phone and whatsapp
                        Row(
                          children: [
                            CircleAvatar(
                              backgroundColor: Colors.white,
                              backgroundImage: NetworkImage(agent.image ?? ''),
                              radius: 28,
                            ),
                            const SizedBox(
                              width: 10,
                            ),
                            Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    agent?.name ?? '',
                                    style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                        fontSize: 16),
                                  ),
                                  Row(
                                    children: [
                                      IconButton(
                                        onPressed: () async {
                                          try {
                                            final phone = appConfiguration
                                                    ?.results['phone'] ??
                                                '+97142454824';
                                            final String phoneNumber =
                                                phone.replaceAll(
                                                    RegExp(r'[^0-9]'), '');

                                            log('Call Phone $phoneNumber');

                                            await callNumber(phoneNumber);
                                            // await launchUrlString("tel:$phoneNumber");
                                          } catch (e) {
                                            log('Cannot open $e');
                                          }
                                        },
                                        icon: const Icon(
                                          CupertinoIcons.phone,
                                          size: 20,
                                        ),
                                      ),
                                      const SizedBox(
                                        width: 10,
                                      ),
                                      IconButton(
                                        onPressed: () async {
                                          try {
                                            final String whatsapp =
                                                '${appConfiguration?.results['whatsapp'] ?? '+97142454824'}';

                                            var whatsappUrl = Uri.parse(
                                                "https://wa.me/$whatsapp");

                                            await launchUrl(whatsappUrl,
                                                mode: LaunchMode
                                                    .externalApplication);
                                          } catch (e) {
                                            log('Cannot open $e');
                                          }
                                        },
                                        icon: const Icon(
                                          FontAwesomeIcons.whatsapp,
                                          size: 20,
                                        ),
                                      ),
                                    ],
                                  ),
                                ])
                          ],
                        )
                      ],
                    ),
                  )
              ],
            )),
          )));
}

void navigateToMap(context, {required double lat, required double lng}) async {
  try {
    log('LLLLLat $lat, Long $lng');

    // await launchUrlString(
    //     'https://www.google.com/maps/search/?api=1&query=$lat,$lng');

    final availableMaps = await MapLauncher.installedMaps;

    await availableMaps.first.showMarker(
      coords: Coords(lat, lng),
      title: 'Navigate to Location',
    );
  } catch (e) {
    log('Could not launch $e');
    snackbar(AppLocalizations.of(context).translate('no location found'));
  }
}

class _ViewLocationOnMap extends StatelessWidget {
  final double? lat;
  final double? lng;

  const _ViewLocationOnMap({Key? key, this.lat, this.lng}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
        child: GestureDetector(
            onTap: () async {
              if (lat != null && lng != null) {
                navigateToMap(context, lat: lat!, lng: lng!);
              }
            },
            child: Container(
              height: 40,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                  color: const Color(0xffF3F7FB),
                  borderRadius: BorderRadius.circular(5)),
              child: Container(
                  padding: const EdgeInsets.all(5),
                  child: Center(
                      child: Text(
                    AppLocalizations.of(context)
                        .translate('View Location on Google maps'),
                    style: const TextStyle(color: Color(0xff0852AB)),
                  ))),
            )));
  }
}

class _OpenHolidayHomeVideoDetails extends StatelessWidget {
  final VideoDetailsModel? video;

  const _OpenHolidayHomeVideoDetails({Key? key, required this.video})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
        child: GestureDetector(
            onTap: () async {
              Navigator.of(context).push(MaterialPageRoute(
                  builder: (BuildContext context) => HoldayHomeDetails(
                        video: video,
                      )));
            },
            child: Container(
              height: 40,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                  color: const Color(0xFF27b4a8),
                  borderRadius: BorderRadius.circular(5)),
              child: Container(
                  padding: const EdgeInsets.all(10),
                  child: Center(
                      child: Text(
                    AppLocalizations.of(context).translate('Watch Video'),
                    style: const TextStyle(color: Colors.white),
                  ))),
            )));
  }
}

class _SendRequest extends StatelessWidget {
  final int? id;
  final int? rmsCategoryId;
  final int agentId;
  final String? numOfRooms;
  final String? propertyName;
  final num? startPrice;

  const _SendRequest(
      {Key? key,
      required this.id,
      required this.rmsCategoryId,
      required this.agentId,
      required this.startPrice,
      required this.propertyName,
      required this.numOfRooms})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
        child: GestureDetector(
            onTap: () async {
              final authController = AuthController();

              await authController.isloggedin();

              authController.isLogged == true
                  ? Navigator.of(context).pushReplacement(MaterialPageRoute(
                      builder: (BuildContext context) => SendHolidayRequest(
                            id!,
                            rmsCategoryId: rmsCategoryId,
                            numOfRooms: numOfRooms,
                            agentId: agentId,
                            startPrice: startPrice,
                        videoName:
                          )))
                  : snackbar(AppLocalizations.of(context)
                      .translate('Please login first'));
            },
            child: Container(
              height: 40,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                  color: const Color(0xFF27b4a8),
                  borderRadius: BorderRadius.circular(5)),
              child: Container(
                  padding: const EdgeInsets.all(10),
                  child: Center(
                      child: Text(
                    AppLocalizations.of(context).translate('Make Booking'),
                    style: const TextStyle(color: Colors.white),
                  ))),
            )));
  }
}
