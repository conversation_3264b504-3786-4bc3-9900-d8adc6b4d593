import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:google_cloud_translation/google_cloud_translation.dart';
import 'package:page/src/core/utils/dynamic_links.dart';
import 'package:page/src/core/utils/logger.dart';
import 'package:page/src/features/views/ad_details/services/cached_video_services.dart';
import 'package:page/src/features/views/ad_details/video_widget.dart';
import 'package:page/src/features/views/holiday_home_details/widgets/call_actions_widget.dart';
import 'package:page/src/features/views/holiday_home_details/widgets/details.dart';
import 'package:page/src/features/views/holiday_home_details/widgets/discussion.dart';
import 'package:page/src/features/views/holiday_home_details/widgets/join_discussion.dart';
import 'package:page/src/features/views/splash_screen/services/splash_services.dart';
import 'package:share_plus/share_plus.dart';
import 'package:video_player/video_player.dart';

import '../../../core/localization/app_localizations.dart';
import '../../../core/response/generalResponse.dart';
import '../../../core/services/api.dart';
import '../../../core/shared_widgets/back_button.dart';
import '../../../core/shared_widgets/shared_widgets.dart';
import '../../controllers/auth_controller.dart';
import '../../controllers/currency_controller.dart';
import '../../controllers/language_controller.dart';
import '../../models/agents.dart';
import '../../models/category.dart';
import '../../models/discussions.dart';
import '../send_holiday_request/send_holiday_home_request.dart';
import '../story/widgets/see_more.dart';

bool isactions = false;
bool isCallActions = false;
TextEditingController note = TextEditingController();
TextEditingController discussioncontroller = TextEditingController();

// ignore: must_be_immutable
class HoldayHomeDetails extends StatefulWidget {
  final VideoDetailsModel? video;

  const HoldayHomeDetails({super.key, required this.video});

  @override
  State<StatefulWidget> createState() {
    return _HoldayHomeDetails();
  }
}

class _HoldayHomeDetails extends State<HoldayHomeDetails> {
  VideoPlayerController? _controller;

  // => _controller;

  bool isdiscussion = false;
  int imageindex = 0;
  int? imageindex2;
  TranslationModel? _translated;
  Translation? _translation;
  String? text;
  int index2 = -1;
  LanguageController language = LanguageController();
  List<Categoryimages> images = [];
  List<Discussions> dis = [];
  int pagenumber = 1;
  int? code;
  String msg = 'Loading';
  AuthController authController = AuthController();
  CurrencyController currencyController = CurrencyController();
  bool isload = false;

  // String? video;
  // String? name;
  // var description, location, type, phone;
  // var startprice, endprice, size, roomnumber;
  // int agentId = 0;

  // double? lat, lng;
  // int isfavouriate = 0;
  bool ismute = false;
  int? holidayHomeId;

  int get id => widget.video!.id!;

  int? get rmsCategoryId => widget.video?.rmsCategoryId;

  String? get video => widget.video?.video;

  String? get name => widget.video?.name;

  String? get label => widget.video?.category?.name;

  String? get phone => widget.video?.phone;

  int? get rating => widget.video?.rating;

  String? get description => widget.video?.description;

  String? get location => widget.video?.locationName;

  String? get website => widget.video?.website;

  String? get instagram => widget.video?.instagram;

  String? get greviewlink => widget.video?.greviewlink;

  String? get greviewName => widget.video?.greviewName;

  String? get category => widget.video?.category?.name;

  num? get startprice => widget.video?.startprice;

  num? get endprice => widget.video?.endprice;

  num? get price => widget.video?.price;

  double? get lat => widget.video?.latitude;

  double? get lng => widget.video?.longitude;

  int? get agentId => widget.video?.agent?.id;
  AgentModel? get agent => widget.video?.agent;

  String? get agentName => widget.video?.agent?.name;

  int? get size => widget.video?.size;

  String? get roomnumber => widget.video?.rooms;

  String? get type => widget.video?.type;

  int? get isfavouriate => widget.video?.isFav;

  set isfavouriate(int? value) => widget.video?.isFav = value;

  getdiscussions() async {
    progrsss(context);
    pr!.show();
    dis.clear();
    await Api.gethoildayhomediscussions(pagenumber, 20, id).then((value) {
      value != null
          ? setState(() {
              dis.addAll(value.dscussions);
              code = value.code;
              msg = value.msg;

              // isload = true;
            })
          // ignore: unnecessary_statements
          : null;
      discussionHolidayHome(context,
          dis: dis,
          code: code,
          msg: msg,
          isdiscussion: isdiscussion,
          discussioncontroller: discussioncontroller,
          text: text,
          index2: index2,
          translated: _translated,
          translation: _translation,
          id: id, function: () {
        setState(() {
          dis = [];
          getdiscussions();

          discussioncontroller.text = "";
        });
      });
    });
  }

  @override
  void initState() {
    initPlayer();

    authController.isloggedin();
    currencyController.getcuurentcurrency(context);
    print("type:holiday");
    print(id);
    _translation = Translation(
      apiKey: 'AIzaSyCXOO147BdbuceLIl8Z8D5Jxru2Vjhqd4Q',
    );
    language.getcuurentlanguage();
    super.initState();
  }

  // void initPlayer() async {
  //   //? Check if video had init more than 6 so dispose it
  //   // if (videoPlayerController.length > 5) {
  //   //   await SplashServices().disposeHomeVideoControllersExceptOneVideo(
  //   //     videoId: id!,
  //   //   );
  //   // }
  //   //? listen to errors
  //   _controller = VideoPlayerController.networkUrl(
  //     Uri.parse(video!),
  //   );
  //
  //   if (_controller?.value.isInitialized ?? false) {
  //     _controller?.play();
  //   } else {
  //     _controller = VideoPlayerController.networkUrl(
  //       Uri.parse(video!),
  //     );
  //
  //     try {
  //       log('Errofsdhdfhfhsgwd');
  //
  //       await _controller?.initialize();
  //
  //       _controller?.play();
  //     } catch (e) {
  //       log('ErrofssfrRR: $e');
  //       // SplashServices().disposeHomeVideoControllersExceptOneVideo(
  //       //   videoId: id,
  //       // );
  //     }
  //   }
  //
  //   setState(() {
  //     isload = true;
  //   });
  // }
  final cachedVideoServices = CachedVideoServices();

  void initPlayer() async {
    Future.delayed(const Duration(seconds: cacheDisposeDuration), () async {
      if (!(_controller?.value.isInitialized ?? false)) {
        await Future.microtask(() => cachedVideoServices.disposeCache());
      }
    });

    log('asfasfssaffa ${video}');

    final fileInfo = await Future.microtask(
        () => cachedVideoServices.checkVideoIfCached(video!));

    if (fileInfo == null) {
      _controller = VideoPlayerController.networkUrl(
        Uri.parse(video!),
      );

      Future.microtask(() => cachedVideoServices.cacheVideo(video!));

      Log.i('CachedVideoServices: $video');
    } else {
      _controller = VideoPlayerController.file(fileInfo.file);

      Log.w('AlreadyCached: $video');
    }

    if (_controller?.value.isInitialized ?? false) {
      _controller?.play();
    } else {
      try {
        await Future.microtask(() => _controller?.initialize());

        _controller?.play();
      } catch (e) {
        Log.e('ErrorOccurred: $video');
      }
    }

    setState(() {
      isload = true;
    });
  }

  @override
  void dispose() {
    _controller?.pause();
    _controller?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isEng = isEnglish(context);

    return WillPopScope(
        onWillPop: () {
          SplashServices().initControllersForHomeData();

          if (_controller!.value.isPlaying) {
            _controller!.pause();
            // _controller = null;
            // _controller?.dispose();
          }
          return Future.value(true);
        },
        child: SafeArea(
            child: Scaffold(
          backgroundColor: Colors.black,
          body: Center(
            child: SizedBox(
              width: kIsWeb ? 500 : null,
              child: Stack(
                alignment: Alignment.bottomCenter,
                children: <Widget>[
                  _controller != null
                      ? SizedBox(
                          height: MediaQuery.sizeOf(context).height,
                          width: MediaQuery.sizeOf(context).width,
                          child: _controller!.value.isInitialized
                              ? VideoPlayer(_controller!)
                              : const SizedBox())
                      : const SizedBox(),

                  // if (!(_controller?.value.isInitialized ?? false)) ...[
                  //   Positioned.fill(
                  //     child: Shimmer.fromColors(
                  //         baseColor: Colors.black.withOpacity(0.5),
                  //         highlightColor: Colors.grey.withOpacity(0.5),
                  //         child: Container(
                  //           color: Colors.black.withOpacity(0.5),
                  //         )),
                  //   ),
                  // ],

                  Positioned(
                      left: 10,
                      right: 10,
                      top: 20,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          BackButtonWidget(
                            onTap: () {
                              SplashServices().initControllersForHomeData();
                              if (_controller?.value.isPlaying ?? false) {
                                _controller!.pause();
                              }
                              setState(() {
                                isactions = false;
                                isCallActions = false;
                              });
                            },
                          ),
                          InkWell(
                              onTap: () {
                                setState(() {
                                  ismute = !ismute;
                                  ismute
                                      ? _controller!.setVolume(0.0)
                                      : _controller!.setVolume(30.0);
                                });
                                // _controller!.setVolume(0.0);
                              },
                              child: !ismute
                                  ? const Icon(
                                      Icons.volume_down,
                                      color: Colors.white,
                                    )
                                  : const Icon(
                                      Icons.volume_off,
                                      color: Colors.white,
                                    ))
                        ],
                      )),
                  if (!isEng)
                    Positioned(bottom: 65, right: 30, child: _nameDetails())
                  else
                    Positioned(bottom: 65, left: 25, child: _nameDetails()),
                  _controller != null
                      ? Padding(
                          padding: const EdgeInsetsDirectional.only(
                            bottom: 25,
                            top: 9,
                            start: 10,
                            end: 10,
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: IconButton(
                                    onPressed: () {
                                      _controller!.value.isPlaying
                                          ? _controller!.pause()
                                          : _controller!.play();
                                      setState(() {});
                                    },
                                    icon: Icon(
                                      _controller!.value.isPlaying
                                          ? Icons.pause
                                          : Icons.play_arrow,
                                      color: Colors.white,
                                    )),
                              ),
                              const SizedBox(width: 10),
                              Expanded(
                                flex: 8,
                                child: VideoProgressIndicator(
                                  _controller!,
                                  allowScrubbing: true,
                                  colors: VideoProgressColors(
                                      playedColor:
                                          _controller!.value.isInitialized
                                              ? Colors.white
                                              : const Color(0xFF27b4a8),
                                      backgroundColor:
                                          Colors.white.withOpacity(0.44)),
                                ),
                              ),
                              const SizedBox(width: 10),
                              Expanded(
                                child: FittedBox(
                                  fit: BoxFit.scaleDown,
                                  child: Text(
                                    _controller!.value.duration
                                        .toString()
                                        .substring(
                                            _controller!.value.duration
                                                    .toString()
                                                    .indexOf(":") +
                                                1,
                                            _controller!.value.duration
                                                .toString()
                                                .indexOf(".")),
                                    style: const TextStyle(color: Colors.white),
                                    maxLines: 1,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        )
                      : Container(),
                  //FURTHER IMPLEMENTATION  Positioned(
                  if (!isEng)
                    Positioned(
                        bottom: 75, left: 20, child: _columnDetailsWidget())
                  else
                    Positioned(
                        bottom: 75, right: 20, child: _columnDetailsWidget()),

                  //? linear progress indicator
                  if (!(_controller?.value.isInitialized ?? false))
                    Positioned(
                      bottom: 40,
                      right: 0,
                      left: 0,
                      child: LinearProgressIndicator(
                        backgroundColor: Colors.black.withOpacity(0.8),
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Colors.white.withOpacity(0.3),
                        ),
                      ),
                    ),

                  isactions
                      ? JoinDiscussionHolidayHome(
                          onShare: () async {
                            try {
                              final String linkPathData =
                                  '?id=$id&type=${'holiday'}&rmsCategoryId=$rmsCategoryId';
                              final dynamicLink =
                                  await DynamicLinkHandler.createDynamicLink(
                                linkPathData,
                              );

                              log('Link: ${dynamicLink.toString()}');

                              Share.share(dynamicLink.toString()).then((value) {
                                setState(() {
                                  isactions = false;
                                });
                              });
                            } catch (e) {
                              log('Eerrrror ${e.toString()}');
                            }
                          },
                          onJoinDiscussion: () {
                            getdiscussions();
                          },
                          lat: lat,
                          lng: lng,
                          onCancel: () {
                            setState(() {
                              isactions = !isactions;
                            });
                          },
                          name: name)
                      : Container(),

                  isCallActions
                      ? CallActionsWidget(
                          currencyController: currencyController,
                          onCancel: () {
                            setState(() {
                              isCallActions = !isCallActions;
                            });
                          },
                          propertyName: name ?? '',
                          propertyPrice: startprice?.toString() ?? '',
                        )
                      : Container(),
                ],
              ),
            ),
          ),
        )));
  }

  Widget _nameDetails() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        name != null
            ? Text(
                name!,
                style: const TextStyle(
                    color: Colors.white, fontWeight: FontWeight.w700),
              )
            : Container(),
        SizedBox(
            width: MediaQuery.of(context).size.width * 0.75,
            child: Text.rich(
              TextSpan(
                style: const TextStyle(
                  color: Colors.white,
                ),
                children: [
                  TextSpan(
                    text: description != null
                        ? description!.length > maxChars
                            ? description?.substring(0, maxChars)
                            : description
                        : '',
                    style: const TextStyle(
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                  TextSpan(
                    text: AppLocalizations.of(context).translate('seemore'),
                    style: const TextStyle(
                        // fontWeight: FontWeight.bold,
                        ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        _controller?.pause();
                        detailsHolidayHome(context,
                            title: name,
                            rmsCategoryId: rmsCategoryId,
                            agentId: agentId,
                            video: video,
                            description: description,
                            roomnumber: roomnumber,
                            startprice: startprice,
                            currencyController: currencyController,
                            location: location,
                            type: type,
                            lat: lat,
                            lng: lng,
                            id: id,
                            agent: agent,
                            size: size);
                      },
                  ),
                ],
              ),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ))
      ],
    );
  }

  Widget _columnDetailsWidget() {
    final Widget svg5 = SizedBox(
        width: 22,
        height: 25,
        child: SvgPicture.asset(
          'assets/Group 7408.svg',
          semanticsLabel: 'Acme Logo',
          fit: BoxFit.cover,
        ));
    final Widget svg6 = SizedBox(
        width: 30,
        height: 25,
        child: SvgPicture.asset(
          'assets/media_icon.svg',
          semanticsLabel: 'Acme Logo',
          width: 13,
          height: 13,
          fit: BoxFit.fill,
          color: Colors.white,
        ));
    return Column(
      children: [
        InkWell(
          onTap: () {
            _controller?.pause();
            authController.isLogged == true
                ? Navigator.of(context).pushReplacement(MaterialPageRoute(
                    builder: (BuildContext context) => SendHolidayRequest(id,
                        rmsCategoryId: rmsCategoryId,
                        numOfRooms: roomnumber,
                        agentId: agentId!,
                        startPrice: startprice,
                        propertyName: name)))
                : snackbar(AppLocalizations.of(context)
                    .translate('Please login first'));
          },
          child: svg5,
        ),
        const SizedBox(
          height: 20,
        ),
        InkWell(
          onTap: () {
            _controller?.pause();

            setState(() {
              isCallActions = !isCallActions;
            });
          },
          child: const Icon(
            CupertinoIcons.phone,
            color: Colors.white,
            size: 25,
          ),
        ),
        // InkWell(
        //   onTap: () {
        //     _controller!.pause();
        //
        //     moreimagesrelated(context, id, _controller!);
        //   },
        //   child: svg6,
        // ),
        const SizedBox(
          height: 20,
        ),
        authController.isLogged == true
            ? isfavouriate == 1
                ? InkWell(
                    onTap: () async {
                      GeneralResponse sucessinformation =
                          await Api.removehoildayhomefromfavourite(id);
                      print(sucessinformation.code);
                      if (sucessinformation.code == "1") {
                        snackbar2(AppLocalizations.of(context)
                            .translate('remove from favourite successfuly'));
                        setState(() {
                          isfavouriate = 0;
                          // isfav = true;
                        });
                      } else {
                        snackbar(AppLocalizations.of(context).translate(
                            'Something went wrong, please try again later'));
                      }
                    },
                    child: const Icon(
                      Icons.favorite,
                      color: Colors.white,
                    ))
                : InkWell(
                    onTap: () async {
                      GeneralResponse sucessinformation =
                          await Api.addholidayhomefavourite(id!);
                      print(sucessinformation.code);
                      if (sucessinformation.code == "1") {
                        snackbar2(AppLocalizations.of(context)
                            .translate('add to favourite successfuly'));
                        setState(() {
                          isfavouriate = 1;
                        });
                      } else {
                        snackbar(AppLocalizations.of(context).translate(
                            'Something went wrong, please try again later'));
                      }
                      // snackbar('This service has not been activated');
                    },
                    child: const Icon(
                      Icons.favorite_outline,
                      color: Colors.white,
                    ))
            : InkWell(
                onTap: () async {
                  snackbar(AppLocalizations.of(context)
                      .translate('Please login first'));

                  // snackbar('This service has not been activated');
                },
                child: const Icon(
                  Icons.favorite_outline,
                  color: Colors.white,
                )),
        const SizedBox(
          height: 15,
        ),
        GestureDetector(
            onTap: () {
              _controller!.pause();
              setState(() {
                isactions = !isactions;
              });
            },
            child: Container(
              color: Colors.transparent,
              child: SvgPicture.asset(
                'assets/send.svg',
                color: Colors.white,
                // fit: BoxFit.cover,
              ),
            ))
        // const Icon(Icons.more_horiz,
        //     color: Colors.white, size: 30)))
      ],
    );
  }
}
