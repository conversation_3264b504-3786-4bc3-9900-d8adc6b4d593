import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:lottie/lottie.dart';
import 'package:page/src/core/extensions/extensions.dart';
import 'package:page/src/features/controllers/language_controller.dart';
import 'package:page/src/features/models/booked_model.dart';
import 'package:page/src/features/views/send_holiday_request/success_request_screen.dart';

import '../../../core/localization/app_localizations.dart';
import '../../../core/services/api.dart';
import '../../../core/shared_widgets/shared_widgets.dart';
import '../../controllers/currency_controller.dart';

String numOfRooms(rooms) =>
    rooms != null ? rooms?.replaceAll('[', '').replaceAll(']', '') : '0';

// ignore: must_be_immutable
class SendHolidayRequest extends StatefulWidget {
  final int id;
  final int? rmsCategoryId;
  final String? numOfRooms;
  final int agentId;
  final num? startPrice;

  const SendHolidayRequest(
    this.id, {
    super.key,
    required this.rmsCategoryId,
    required this.numOfRooms,
    required this.agentId,
    required this.startPrice,
  });

  @override
  _SendRequest createState() => _SendRequest();
}

class _SendRequest extends State<SendHolidayRequest> {
  CurrencyController currencyController = CurrencyController();

  bool isLoading = false;

  List<BookedModel> bookedDays = [];
  List<(String date, num amount)> priceByDateDetails = [];

  void getBookedDays() async {
    setState(() {
      isLoading = true;
    });

    bookedDays = await Api.getBookedDays(widget.id);

    setState(() {
      isLoading = false;
    });
  }

  @override
  void initState() {
    currencyController.getcuurentcurrency(context);
    getBookedDays();
    super.initState();
  }

  final format2 = DateFormat("yyyy-MM-dd");

  // TextEditingController startdateController = TextEditingController();
  // TextEditingController enddateController = TextEditingController();
  TextEditingController promocode = TextEditingController();
  TextEditingController peopleController = TextEditingController();
  TextEditingController childrenController = TextEditingController(text: '0');
  TextEditingController userNote = TextEditingController(text: '');
  bool isload = false;
  bool isPriceLoading = false;
  bool isfilstartdate = false;
  bool isfilenddate = false;
  String? currentvalue3;
  int? code;
  String? msg;
  Map<String, dynamic>? pricerequest;
  num total = 0.0;
  int? promocodeid;
  int usepromocode = 0;

  // DateTime? calculatedDateTime;

  ValueNotifier<List<DateTime?>> selectedDates =
      ValueNotifier<List<DateTime?>>([]);

  // arrival time, departure time
  ValueNotifier<String> arrivalTime = ValueNotifier<String>('15:00');
  ValueNotifier<String> departureTime = ValueNotifier<String>('11:00');

  void getpricerequest(int id) async {
    if (selectedDates.value.isEmpty) {
      return;
    }

    final startDate = selectedDates.value.first!;

    final endDate = format2.format(selectedDates.value.last!);

    // mke sure start date is after end date
    if (startDate.isAfter(selectedDates.value.last!)) {
      return;
    }

    final isSameDate = startDate.isSameDay(selectedDates.value.last!);

    if (isSameDate) {
      return;
    }

    final date = format2.format(startDate);

    setState(() {
      isPriceLoading = true;
    });

    await Api.getholidayhomerequestprice(
            id,
            date,
            endDate,
            promocodeid,
            numOfRooms(widget.numOfRooms),
            peopleController.text,
            childrenController.text)
        .then((value) {
      value != null
          ? setState(() {
              code = value.code;
              msg = value.error;
              pricerequest = value.results;
              total = pricerequest?['total_amount'] ?? 0;
              //   "rms_response": {
              //             "rateBreakdown": [
              //                 {
              //                     "theDate": "2024-11-26 00:00:00",
              //                     "baseRateAmount": 0,}]}

              priceByDateDetails = List<Map>.from(pricerequest?['rms_response'])
                  .map(
                    (e) => (
                      (e['theDate'] ?? '') as String,
                      (e['baseRateAmount'] ?? 0) as num
                    ),
                  )
                  .toList();
              isPriceLoading = false;
            })
          : null;
    });
  }

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Scaffold(
      appBar: AppBar(
        backgroundColor: const Color(0xFF27b4a8),
        centerTitle: true,
        title: Container(
          padding: const EdgeInsets.only(left: 20, right: 20),
          child: Text(
            AppLocalizations.of(context).translate(
              'Send Request',
            ),
            style: TextStyle(
              fontFamily: isEnglish(context) ? 'Roboto' : 'Tajawal',
            ),
          ),
        ),
      ),
      body: isLoading
          ? buildLoadingWidget()
          : Container(
              padding: const EdgeInsets.only(left: 20, right: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(
                    height: 20,
                  ),
                  Text(
                    AppLocalizations.of(context).translate('Your Inquiry'),
                    style: const TextStyle(fontSize: 13),
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Container(
                    height: 100,
                    decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(3)),
                    child: Container(
                        decoration: BoxDecoration(
                            borderRadius:
                                const BorderRadius.all(Radius.circular(5)),
                            border:
                                Border.all(color: Colors.black12, width: 1.0)),
                        child: TextFormField(
                          controller: userNote,
                          onTapOutside: (focusNode) {
                            FocusScope.of(context).unfocus();
                          },
                          maxLines: 5,
                          decoration: InputDecoration(
                              contentPadding: const EdgeInsets.only(
                                  left: 20, right: 20, top: 20),
                              hintText: AppLocalizations.of(context)
                                  .translate('Enter Your Inquiry'),
                              hintStyle: const TextStyle(
                                  color: Colors.grey, fontSize: 16),
                              border: InputBorder.none),
                        )),
                  ),
                  const Spacer(),
                  !isload
                      ? _sendRequestButton()
                      : Center(
                          child: Lottie.asset(
                              'assets/59218-progress-indicator.json',
                              height: 50,
                              width: 50)),
                  const SizedBox(
                    height: 20,
                  ),
                ],
              ),
            ),
    ));
  }

  Widget _sendRequestButton() {
    return Center(
        child: GestureDetector(
            onTap: () async {
              if (userNote.text.isEmpty) {
                snackbar(AppLocalizations.of(context)
                    .translate('Please enter your inquiry'));
                return;
              }

              setState(() {
                isload = !isload;
              });

              Map<String, dynamic> data = {
                "video_id": widget.id,
                "user_note": userNote.text,
                "request_date": DateTime.now().toIso8601String(),
                // "start_date":
                //     "${format2.format(selectedDates.value.first!)} ${arrivalTime.value}:00",
                // "end_date":
                //     "${format2.format(selectedDates.value.last!)} ${departureTime.value}:00",

                // "num_days": pricerequest!['num_days'],
                // "number_people": peopleController.text,
                // "number_child": childrenController.text,
                // "baseRateTax": pricerequest!['baseRateTax'],
                // "rms_promo_id": pricerequest!['rms_promo_id'],
                // "promo_code": promocodeid,
                // 'number_room': numOfRooms(widget.numOfRooms),
                // 'subtotal': pricerequest!['subtotal']?.toStringAsFixed(2),
                // 'total': pricerequest!['total_amount']?.toStringAsFixed(2),
                // 'vat': pricerequest!['vat']?.toStringAsFixed(2),
                // 'fee': pricerequest!['fee']?.toStringAsFixed(2),
                // 'discount': pricerequest!['discount']?.toStringAsFixed(2),
                // "periods": periods,
                // "num_not_common_days": pricerequest!['num_not_common_days'],
                // "normal_price": pricerequest!['normal_price'],
              };

              log('DSATAAAA $data');
              var res = await Api.sendrequestholidayhome(data);
              // var res = await Api.sendrequestholidayhome(data);

              final sucessinformation = res.$1;
              final request = res.$2;

              print(sucessinformation.code);

              if (sucessinformation.code == "1") {
                snackbar2(AppLocalizations.of(context)
                    .translate('Request send successfuly'));

                Navigator.of(context).pushReplacement(
                  MaterialPageRoute(
                      builder: (context) => SuccessRequestScreen(
                            isFromSendRequest: true,
                            holidayHome: request!,
                          )),
                );
              } else {
                snackbar(sucessinformation.msg!);
              }
              setState(() {
                isload = !isload;
              });
            },
            child: Container(
              height: 50,
              width: MediaQuery.of(context).size.width,
              decoration: BoxDecoration(
                  color: const Color(0xFF27b4a8),
                  borderRadius: BorderRadius.circular(10)),
              child: Container(
                  padding: const EdgeInsets.all(10),
                  child: Center(
                      child: Text(
                    AppLocalizations.of(context).translate('Send Request'),
                    style: const TextStyle(color: Colors.white),
                  ))),
            )));
  }
}
